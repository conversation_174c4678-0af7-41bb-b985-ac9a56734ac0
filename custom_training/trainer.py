"""
* Copyright (C) DeepReality.AI  - All Rights Reserved.
* Project - control-line
* THE CONTENTS OF THIS PROJECT ARE PROPRIETARY AND CONFIDENTIAL.
* UNAUTHORIZED COPYING, TRANSFERRING OR REPRODUCTION OF THE CONTENTS OF THIS PROJECT, VIA ANY MEDIUM IS STRICTLY PROHIBITED.
*
* The receipt or possession of the source code and/or any parts thereof does not convey or imply any right to use them
* for any purpose other than the purpose for which they were provided to you.
*
* The software is provided "AS IS", without warranty of any kind, express or implied, including but not limited to
* the warranties of merchantability, fitness for a particular purpose and non infringement.
* In no event shall the authors or copyright holders be liable for any claim, damages or other liability,
* whether in an action of contract, tort or otherwise, arising from, out of or in connection with the software
* or the use or other dealings in the software.
*
* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.

@file trainer.py
@module LLM_SFT_Text_Trainer
<AUTHOR> guptha talupula
@description This file contains the trainer class for the trainer, handles the hyperparameters from the API and invokes the training with custom dataset.
"""

import sys
sys.dont_write_bytecode = True
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import logging
import os
import datasets
import transformers
import shutil

from google.cloud import storage
from datasets import load_dataset
from transformers import set_seed
from transformers.trainer_utils import get_last_checkpoint
from trl import ModelConfig, ScriptArguments, SFTTrainer, TrlParser, get_peft_config, setup_chat_format

from src.open_r1.configs import SFTConfig
from src.open_r1.utils import get_model, get_tokenizer
from src.open_r1.utils.callbacks import get_callbacks
from src.open_r1.utils.wandb_logging import init_wandb_training
from CustomData import CustomDataConfig

logger = logging.getLogger(__name__)



class LLM_Trainer():
    def __init__(self, script_args, training_args, model_args, datafiles):
        self.script_args = script_args
        self.training_args = training_args
        self.model_args = model_args
        self.datafiles = datafiles
        self.trained_model_path = self.training_args.output_dir 
        self.pretrained_model_path = self.training_args.model_name_or_path
        self.bucket_name = self.datafiles.bucket_name
        self.training_data_paths = None
        self.session_id = self.datafiles.session_id #TODO get session ID from params
        self.model_upload_blob_name = self.datafiles.model_upload_blob_name
        self.domain_model_name = self.datafiles.domain_model_name
        
    # Define storage operations, downloading and uploading data handling.
    # Download pretrained model from gcp storage.
    def download_folder(self):
        """
        Downloads all files under a "folder" in GCS.
        """
        storage_client = storage.Client()
        bucket = storage_client.bucket(self.bucket_name)
        blobs = bucket.list_blobs(prefix=self.pretrained_model_path)
        for blob in blobs:
            # Skip if it's a "directory marker"
            if blob.name.endswith("/"):
                continue
            # Local path (preserve folder structure)
            # local_path = os.path.join(destination_folder, os.path.relpath(blob.name, source_folder))
            local_path = self.pretrained_model_path
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            blob.download_to_filename(local_path)
            print(f"Downloaded {blob.name} → {local_path}")
        logger.info(f"Downloaded {self.pretrained_model_path} from {self.bucket_name}")

    # Download data from gcp storage.
    def download_file_from_gcp(self):
        """    
        Downloads all files from GCS.
        """
        if self.training_data_paths is not None:
            client = storage.Client()
            bucket = client.bucket(self.bucket_name)
            for full_path in self.training_data_paths:
                filepath, filename = os.path.split(full_path)
                os.makedirs(os.path.dirname(full_path), exist_ok=True)
                blob = bucket.blob(os.path.join(filepath,filename))
                blob.download_to_filename(full_path)
                logger.info(f"Downloaded {filename} from {self.bucket_name}/{os.path.join(filepath, filename)}")
        else:
            raise ValueError(f"No data files to download, training_data_paths is None")
    
    # Upload trained models to gcp storage
    def upload_models_to_gcp(self):
        """    
        Uploads all files to GCS.
        """
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "gcp-storage.json"
        
        # Create a client
        storage_client = storage.Client()
        # Get bucket
        bucket = storage_client.bucket(self.bucket_name)
        # Upload files
        for root, _, files in os.walk(self.trained_model_path):
            for file_name in files:
                local_path = os.path.join(root, file_name)
                # Preserve folder structure in GCS
                relative_path = os.path.relpath(local_path, self.trained_model_path)
                blob_path = os.path.join(self.model_upload_blob_name, relative_path)
                blob = bucket.blob(blob_path)
                blob.upload_from_filename(local_path)
                print(f"Uploaded {local_path} → gs://{self.bucket_name}/{blob_path}")
        
        logger.info(f"File {self.trained_model_path} uploaded to gs://{self.bucket_name}/{self.model_upload_blob_name}")
    
    def PDProcess(self, example):
        # Extract the relevant content
        input = example['model_input']
        thinking = example['model_output _think_part']
        answer = example['model_output_answer_part']

        messages = [
            {"role": "user", "content": "You are given a document. Extract the required fields into the JSON schema below. Instructions: \n "+model_prompt_input+ "\n Document: \n " + input},
            {"role": "assistant", "content": thinking + " " + answer},
        ]   

        formatted_text = self.tokenizer.apply_chat_template(
        messages,
        tokenize=False,
        add_generation_prompt=False
        )
        
        return {'text': formatted_text}
    
    def setup_training(self):
        # Set seed for reproducibility
        set_seed(self.training_args.seed)
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "gcp-storage.json"
        
        os.makedirs(self.session_id, exist_ok=True)
    
        logging.basicConfig(
            format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
            filename=self.session_id + '/'+ self.session_id +'.log', ##adding file name so that logs will be saved 
            datefmt="%Y-%m-%d %H:%M:%S",
            # handlers=[logging.StreamHandler(sys.stdout)],
        )
        log_level = self.training_args.get_process_log_level()
        logger.setLevel(log_level)
        datasets.utils.logging.set_verbosity(log_level)
        transformers.utils.logging.set_verbosity(log_level)
        transformers.utils.logging.enable_default_handler()
        transformers.utils.logging.enable_explicit_format()
        
        logger.info(f"Model parameters {self.model_args}")
        logger.info(f"Script parameters {self.script_args}")
        logger.info(f"Training parameters {self.training_args}")
        logger.info(f'Datafiles parameters {self.datafiles}')
        
        # Check for last checkpoint
        last_checkpoint = None
        if os.path.isdir(self.training_args.output_dir):
            last_checkpoint = get_last_checkpoint(self.training_args.output_dir)
        if last_checkpoint is not None and self.training_args.resume_from_checkpoint is None:
            logger.info(f"Checkpoint detected, resuming training at {last_checkpoint=}.")

        if "wandb" in self.training_args.report_to:
            init_wandb_training(self.training_args)
            
        self.training_data_paths = list(self.datafiles.data_files.values())
        self.download_file_from_gcp() # Download train data, validation data from gcp storage
        
        self.training_data_paths = list(self.datafiles.model_prompts)
        self.download_file_from_gcp() # Download model prompts from gcp storage
        
        self.download_folder() # Download pretrained model from gcp storage
        
        csvdata = load_dataset('csv', data_files=self.datafiles.data_files)
        
        tokenizer = get_tokenizer(model_args, training_args)
        tokenizer.padding_side = 'left'
        with open(self.datafiles.model_prompts, 'r') as file:
            model_prompt_input = file.read()
        
        if self.domain_model_name == "PropertyDocument":
            
        
        
        
        
        
        
        
        
if __name__ == "__main__":
    
    parser = TrlParser((ScriptArguments, SFTConfig, ModelConfig,CustomDataConfig))
    script_args, training_args, model_args,datafiles = parser.parse_args_and_config()
    # Set seed for reproducibility
    set_seed(training_args.seed)

    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "gcp-storage.json"
    #TODO get a session ID from params, use that to store all the files for a training session
    session_id = custom_train_config_params.session_id
    os.makedirs(session_id, exist_ok=True)
    log_file_path = os.path.join(session_id, 'training.log')
    
    logging.basicConfig(
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        filename=log_file_path, ##adding file name so that logs will be saved 
        datefmt="%Y-%m-%d %H:%M:%S",
        # handlers=[logging.StreamHandler(sys.stdout)],
    )
    log_level = training_args.get_process_log_level()
    logger.setLevel(log_level)
    datasets.utils.logging.set_verbosity(log_level)
    transformers.utils.logging.set_verbosity(log_level)
    transformers.utils.logging.enable_default_handler()
    transformers.utils.logging.enable_explicit_format()

    logger.info(f"Model parameters {model_args}")
    logger.info(f"Script parameters {script_args}")
    logger.info(f"Training parameters {training_args}")
    logger.info(f'Datafiles parameters {datafiles}')
    

    #TODO delete pretrrained model, dataset after training
    
    all_gcp_paths = list(datafiles.data_files.values())
    print("Data paths: ", all_gcp_paths)
    self.download_file_from_gcp(all_gcp_paths)
    
    # confinqaDataset= load_dataset('csv', data_files=convfinqa_data_files)
    csvdata = load_dataset('csv', data_files=self.datafiles.data_files)       
    
    ##preprocess function
        ################
    # Load tokenizer
    ################
    tokenizer = get_tokenizer(self.model_args, self.training_args)
    tokenizer.padding_side = 'left'
    with open(self.datafiles.model_promptss, 'r') as file:
        model_prompt_input = file.read()
    
    dataset= csvdata.map(self.PDProcess)
    
    print('Dataset format>>>>>>>>>>>>>>>>>>>>>>>',dataset)
    
    print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> len of dataset",len(dataset['train']))
    print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> len of dataset",len(dataset['validation']))
    
    ###################
    # Load model
    ###################
    logger.info("*** Loading model ***")
    model = get_model(self.model_args, self.training_args)

    if tokenizer.chat_template is None:
        logger.info("No chat template provided, using ChatML.")
        model, tokenizer = setup_chat_format(model, tokenizer, format="chatml")


    ############################
    # Initialize the SFT Trainer
    ############################
    trainer = SFTTrainer(
        model=model,
        args=self.training_args,
        
        train_dataset=dataset["train"],
        eval_dataset=dataset["validation"] , 
        processing_class=tokenizer,
        peft_config=get_peft_config(self.model_args),
        # peft_config = lora_config,
        callbacks=get_callbacks(self.training_args, self.model_args),
    )

    ###############
    # Training loop
    ###############
    logger.info("*** Train ***")
    checkpoint = None
    if self.training_args.resume_from_checkpoint is not None:
        checkpoint = self.training_args.resume_from_checkpoint
    elif self.last_checkpoint is not None:
        checkpoint = self.last_checkpoint
    
    
    train_result = trainer.train(resume_from_checkpoint=checkpoint)
    # train_result = trainer.train()
    
    metrics = train_result.metrics
    metrics["train_samples"] = len(dataset[self.script_args.dataset_train_split])
    trainer.log_metrics("train", metrics)
    trainer.save_metrics("train", metrics) 
    # trainer.save_state() ##commenting
    logger.info(f"Training metrics: {metrics}") #added
    
    for step_metrics in trainer.state.log_history:
        if "loss" in step_metrics:
            trainer.save_metrics(f"train_step_{step_metrics['step']}", step_metrics)
            logger.info(f'loss metrics {step_metrics}')
            
        if "eval_loss" in step_metrics:
            trainer.save_metrics(f"eval_step_{step_metrics['step']}", step_metrics)
            logger.info(f'eval metrics {step_metrics}')

    ##################################
    # Save model and create model card
    ##################################
    logger.info("*** Save model ***")
    trainer.save_model(self.training_args.output_dir)
    logger.info(f"Model saved to {self.training_args.output_dir}")

    # Save everything else on main process
    kwargs = {
        "dataset_name": self.datafiles.CustomDatasetName,
        "tags": ["open-r1"],
    }
    if trainer.accelerator.is_main_process:
        trainer.create_model_card(**kwargs)
        # Restore k,v cache for fast inference
        trainer.model.config.use_cache = True
        trainer.model.config.save_pretrained(self.training_args.output_dir)
        tokenizer.save_pretrained(self.training_args.output_dir) ##save tokenizer
        
        
    ##push models to gcp
    self.upload_models_to_gcp(source_file_path=self.training_args.output_dir, destination_blob_name='llm_models_test/'+os.path.basename(self.training_args.output_dir))

    #############
    # push to hub
    #############
    # if self.training_args.push_to_hub:
    #     logger.info("Pushing to hub...")
    #     trainer.push_to_hub(**kwargs)










    
    


