# KubeFlow Cred's
mail_id : <EMAIL>
password : 12341234
namespace : kubeflow-admin

app : Retail
project_name : or
model_name : eva_large_dec19_tzue_store
data_name : ml-retail-pipeline-data
golden_dataset : ml-retail-pipeline-test-data
classes : 3668

data_segrigation_ratio : 0.9
min_images_count : 10

img_dimensions: 448

architecture : dinov2_vit_large14

# 0 - similar eans collection format, 1 - grades format, 2 - combine format, 3 - unique EANS from collection format 
data_grades :
  mode : 0
  grades : "1,2,3"


epochs : 20
train_batch_size : 32
val_batch_size : 32
jit_compile : False

warmup_calculation:
  learning_rate_base : 0.03
  total_steps : 13000
  warmup_steps : 10
  init_lr : 0.00004
  wamrup_lr : 0.000001

gpus: 
  gpu_1: False
  gpu_2: False
  gpu_4: True
  gpu_8: False

cloud : gcp