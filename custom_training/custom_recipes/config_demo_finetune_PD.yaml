# Model arguments
# model_name_or_path: Qwen/Qwen2.5-1.5B-Instruct
# model_name_or_path: /shared/narendra/all_model/Qwen2.5-Coder-1.5B
model_name_or_path: /data/mounika/Qwen2.5-7B-Instruct #TODO use this Blob to upload pretrained models "LLM_Base_Models/Qwen2.5-7B-Instruct"

# model_name_or_path: /shared/narendra/all_model/Qwen2.5-7B-Instruct
model_revision: main
torch_dtype: bfloat16
attn_implementation: flash_attention_2
use_cache : false
use_peft : yes
lora_r : 8
lora_alpha : 16
task_type : CAUSAL_LM
lora_dropout : 0.05
# lora_target_modules : ["q_proj","k_proj", "v_proj"]

# Data training arguments
# dataset_name: open-r1/OpenR1-Math-220k
dataset_name: FlareDataset
dataset_num_proc: 48 #48

#CustomDataConfig:
data_files:
  train: "llm_text_training/propertydocstrain.csv"
  validation: "llm_text_training/propertydocval.csv"
CustomDatasetName : propertydoc
model_prompts: ./recipes/prompts/PD_prompt.txt
model_upload_blob_name: "LLM_Custom_Data_Trained_Models/"
bucket_name: "llm-text-training"
session_id: "sessionID"
domain_model_name : "PropertyDocument" # or RentalAgreement, UnderWriting, Retail


# SFT trainer config
bf16: true
do_eval: false
eval_strategy: "steps"
eval_steps : 30
gradient_accumulation_steps: 2   #2,4
gradient_checkpointing: true
gradient_checkpointing_kwargs:
  use_reentrant: true
hub_model_id: Qwen2.5-7B-Instruct-Distill-r8
hub_strategy: every_save
learning_rate: 5.0e-05
log_level: info
logging_steps: 30
logging_strategy: steps
lr_scheduler_type: cosine_with_min_lr
lr_scheduler_kwargs:
  min_lr_rate: 0.1
packing: false
max_length: 16384
max_steps: -1
num_train_epochs: 1
output_dir: sessionID_Qwen2.5-7B-Instruct_PD
overwrite_output_dir: true
per_device_eval_batch_size: 2 #8, 
per_device_train_batch_size: 8
push_to_hub: false
report_to:
- none
save_strategy: "steps" #"steps", "best"
save_steps: 130
save_only_model : true  #no need of resuming model
save_total_limit: 100
seed: 42
use_liger_kernel: true
warmup_ratio: 0.05 #0.1
# main_process_port : 29502