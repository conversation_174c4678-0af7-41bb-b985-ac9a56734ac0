# Model arguments
model_name_or_path : EleutherAI/pythia-1b-deduped #for tokenizer
sft_model_path : EleutherAI/pythia-1b-deduped
reward_model_path : EleutherAI/pythia-1b-deduped
# model_name_or_path: Qwen/Qwen2-0.5B-Instruct
model_revision: main
torch_dtype: bfloat16
attn_implementation: flash_attention_2
use_cache : false
use_peft : yes
lora_r : 32
lora_alpha : 64
task_type : CAUSAL_LM
lora_dropout : 0.05

# Data training arguments
dataset_name : trl-internal-testing/descriptiveness-sentiment-trl-style
dataset_train_split : descriptiveness
dataset_num_proc: 5 #48

local_rollout_forward_batch_size : 1
missing_eos_penalty : 1.0

#training config
bf16: true
do_eval: false
eval_strategy: "steps"
eval_steps : 2000 
gradient_accumulation_steps: 16   #2,4
gradient_checkpointing: true
gradient_checkpointing_kwargs:
  use_reentrant: true

learning_rate: 3e-6
max_steps: -1
num_train_epochs: 100
num_ppo_epochs : 1
num_mini_batches : 1
total_episodes : 10000
output_dir: data/ppo/Qwen2-0.5B-DPO_7k_peft
overwrite_output_dir: true
per_device_eval_batch_size: 4 #8, 
per_device_train_batch_size: 1
push_to_hub: false
report_to:
- none
save_strategy: "steps" #"steps", "best"
save_steps: 1
save_only_model : true  #no need of resuming model
save_total_limit: 100
seed: 42
warmup_ratio: 0.05 #0.1
