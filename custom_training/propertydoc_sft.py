# Copyright 2025 The HuggingFace Team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Supervised fine-tuning script for decoder language models.

Usage:

# One 1 node of 8 x H100s
accelerate launch --config_file=recipes/accelerate_configs/zero3.yaml src/open_r1/sft.py \
    --model_name_or_path Qwen/Qwen2.5-1.5B-Instruct \
    --dataset_name open-r1/OpenR1-Math-220k \
    --learning_rate 2.0e-5 \
    --num_train_epochs 1 \
    --packing \
    --max_seq_length 4096 \
    --per_device_train_batch_size 2 \
    --gradient_accumulation_steps 8 \
    --gradient_checkpointing \
    --bf16 \
    --logging_steps 5 \
    --eval_strategy steps \
    --eval_steps 100 \
    --output_dir data/Qwen2.5-1.5B-Open-R1-Distill
"""

import logging
import os
import sys

import datasets
import transformers
from datasets import load_dataset
from transformers import set_seed
from transformers.trainer_utils import get_last_checkpoint
from trl import ModelConfig, ScriptArguments, SFTTrainer, TrlParser, get_peft_config, setup_chat_format


from src.open_r1.configs import SFTConfig
from main_repo.src.open_r1.utils import get_model, get_tokenizer
from main_repo.src.open_r1.utils.callbacks import get_callbacks
from main_repo.src.open_r1.utils.wandb_logging import init_wandb_training


from CustomData import CustomDataConfig
from google.cloud import storage

logger = logging.getLogger(__name__)





















def main(script_args, training_args, model_args,datafiles):
    # Set seed for reproducibility
    set_seed(training_args.seed)

    ###############
    # Setup logging
    ###############
    
    os.makedirs('data', exist_ok=True)
    
    logging.basicConfig(
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
        filename='data/retailpeft_12.log', ##adding file name so that logs will be saved 
        datefmt="%Y-%m-%d %H:%M:%S",
        # handlers=[logging.StreamHandler(sys.stdout)],
    )
    log_level = training_args.get_process_log_level()
    logger.setLevel(log_level)
    datasets.utils.logging.set_verbosity(log_level)
    transformers.utils.logging.set_verbosity(log_level)
    transformers.utils.logging.enable_default_handler()
    transformers.utils.logging.enable_explicit_format()

    logger.info(f"Model parameters {model_args}")
    logger.info(f"Script parameters {script_args}")
    logger.info(f"Training parameters {training_args}")
    logger.info(f'Datafiles parameters {datafiles}')
    

    # Check for last checkpoint
    last_checkpoint = None
    if os.path.isdir(training_args.output_dir):
        last_checkpoint = get_last_checkpoint(training_args.output_dir)
    if last_checkpoint is not None and training_args.resume_from_checkpoint is None:
        logger.info(f"Checkpoint detected, resuming training at {last_checkpoint=}.")

    if "wandb" in training_args.report_to:
        init_wandb_training(training_args)
    
    
    ################
    #save models to gcp
    ################
    def upload_models_to_gcp(source_file_path, destination_blob_name, bucket_name='llm-storage-1'):
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "gcp-storage.json"
        
        # Create a client
        storage_client = storage.Client()
        # Get bucket
        bucket = storage_client.bucket(bucket_name)
        # Upload files
        for root, _, files in os.walk(source_file_path):
            for file_name in files:
                local_path = os.path.join(root, file_name)

                # Preserve folder structure in GCS
                relative_path = os.path.relpath(local_path, source_file_path)
                blob_path = os.path.join(destination_blob_name, relative_path)

                blob = bucket.blob(blob_path)
                blob.upload_from_filename(local_path)

                print(f"Uploaded {local_path} → gs://{bucket_name}/{blob_path}")
        
        
        
        # print(f"File {source_file_path} uploaded to gs://{bucket_name}/{destination_blob_name}")
        logger.info(f"File {source_file_path} uploaded to gs://{bucket_name}/{destination_blob_name}")
    
    
    
    

    ################
    # Load datasets
    ################
    
    # Download data from storage
    def download_file_from_gcp(filepaths, bucket_name='llm-storage-1'):
        
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "gcp-storage.json"
        
        client = storage.Client()
        bucket = client.bucket(bucket_name)
        for full_path in filepaths:
            filepath, filename = os.path.split(full_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            blob = bucket.blob(os.path.join(filepath,filename))
            blob.download_to_filename(full_path)
            logger.info(f"Downloaded {filename} from {bucket_name}/{os.path.join(filepath, filename)}")
    
    all_gcp_paths = list(datafiles.data_files.values())
    print("Data paths: ", all_gcp_paths)
    download_file_from_gcp(all_gcp_paths)
    
    # confinqaDataset= load_dataset('csv', data_files=convfinqa_data_files)
    csvdata = load_dataset('csv', data_files=datafiles.data_files)       
    
    ##preprocess function
        ################
    # Load tokenizer
    ################
    tokenizer = get_tokenizer(model_args, training_args)
    tokenizer.padding_side = 'left'
    with open(datafiles.model_promptss, 'r') as file:
        model_prompt_input = file.read()

    def PDProcess(example):
        # Extract the relevant content
        input = example['model_input']
        thinking = example['model_output _think_part']
        answer = example['model_output_answer_part']

        messages = [
            {"role": "user", "content": "You are given a document. Extract the required fields into the JSON schema below. Instructions: \n "+model_prompt_input+ "\n Document: \n " + input},
            {"role": "assistant", "content": thinking + " " + answer},
        ]   

        formatted_text = tokenizer.apply_chat_template(
        messages,
        tokenize=False,
        add_generation_prompt=False
        )
        
        return {'text': formatted_text}

    dataset= csvdata.map(PDProcess)
    
    print('Dataset format>>>>>>>>>>>>>>>>>>>>>>>',dataset)
    
    print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> len of dataset",len(dataset['train']))
    print(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> len of dataset",len(dataset['validation']))
    
    ###################
    # Load model
    ###################
    logger.info("*** Loading model ***")
    model = get_model(model_args, training_args)

    if tokenizer.chat_template is None:
        logger.info("No chat template provided, using ChatML.")
        model, tokenizer = setup_chat_format(model, tokenizer, format="chatml")


    ############################
    # Initialize the SFT Trainer
    ############################
    trainer = SFTTrainer(
        model=model,
        args=training_args,
        
        train_dataset=dataset["train"],
        eval_dataset=dataset["validation"] , 
        processing_class=tokenizer,
        peft_config=get_peft_config(model_args),
        # peft_config = lora_config,
        callbacks=get_callbacks(training_args, model_args),
    )

    ###############
    # Training loop
    ###############
    logger.info("*** Train ***")
    checkpoint = None
    if training_args.resume_from_checkpoint is not None:
        checkpoint = training_args.resume_from_checkpoint
    elif last_checkpoint is not None:
        checkpoint = last_checkpoint
    
    
    train_result = trainer.train(resume_from_checkpoint=checkpoint)
    # train_result = trainer.train()
    
    metrics = train_result.metrics
    metrics["train_samples"] = len(dataset[script_args.dataset_train_split])
    trainer.log_metrics("train", metrics)
    trainer.save_metrics("train", metrics) 
    # trainer.save_state() ##commenting
    logger.info(f"Training metrics: {metrics}") #added
    
    for step_metrics in trainer.state.log_history:
        if "loss" in step_metrics:
            trainer.save_metrics(f"train_step_{step_metrics['step']}", step_metrics)
            logger.info(f'loss metrics {step_metrics}')
            
        if "eval_loss" in step_metrics:
            trainer.save_metrics(f"eval_step_{step_metrics['step']}", step_metrics)
            logger.info(f'eval metrics {step_metrics}')

    ##################################
    # Save model and create model card
    ##################################
    logger.info("*** Save model ***")
    trainer.save_model(training_args.output_dir)
    logger.info(f"Model saved to {training_args.output_dir}")

    # Save everything else on main process
    kwargs = {
        "dataset_name": datafiles.CustomDatasetName,
        "tags": ["open-r1"],
    }
    if trainer.accelerator.is_main_process:
        trainer.create_model_card(**kwargs)
        # Restore k,v cache for fast inference
        trainer.model.config.use_cache = True
        trainer.model.config.save_pretrained(training_args.output_dir)
        tokenizer.save_pretrained(training_args.output_dir) ##save tokenizer
        
        
    ##push models to gcp
    upload_models_to_gcp(source_file_path=training_args.output_dir, destination_blob_name='llm_models_test/'+os.path.basename(training_args.output_dir))

    #############
    # push to hub
    #############
    if training_args.push_to_hub:
        logger.info("Pushing to hub...")
        trainer.push_to_hub(**kwargs)


if __name__ == "__main__":
    parser = TrlParser((ScriptArguments, SFTConfig, ModelConfig,CustomDataConfig))
    script_args, training_args, model_args,datafiles = parser.parse_args_and_config()
    
    main(script_args, training_args, model_args,datafiles)
