import pkg_resources
from configuration.load_yaml import load_parms

installed_packages = pkg_resources.working_set
installed_packages_list = sorted(["%s==%s" % (i.key, i.version)for i in installed_packages])

def load_env():

    for i in installed_packages_list:

        if i.split('==')[0] == 'tensorflow':
            tensorflow_version = i.split('==')[1] 
        elif i.split('==')[0] == 'matplotlib':
            matplotlib = i.split('==')[1]
        elif i.split('==')[0] == 'numpy':
            numpy = i.split('==')[1]
        elif i.split('==')[0] == 'pandas':
            pandas = i.split('==')[1]
        elif i.split('==')[0] == 'tensorflow-hub':
            tensorflow_hub = i.split('==')[1]
        elif i.split('==')[0] == 'h5py':
            h5py_version = i.split('==')[1]
        elif i.split('==')[0] == 'scikit-learn':
            scikit_learn = i.split('==')[1]
        elif i.split('==')[0] == 'pip':
            pip_version = i.split('==')[1]
        elif i.split('==')[0] == 'pytz':
            pytz = i.split('==')[1]
        elif i.split('==')[0] == 'psycopg2-binary':
            psycopg2_binary = i.split('==')[1]
        
        # Cloud Dependencies
        load_config_file = load_parms() 
        if load_config_file.cloud == 'gcp':
            if i.split('==')[0] == 'gcloud':
                cloud_version = i.split('==')[1]
        elif load_config_file == 'azure':
            if i.split('==')[0] == 'azure':
                cloud_version = i.split('==')[1]

    return pip_version, tensorflow_hub, tensorflow_version, matplotlib, numpy, pandas, h5py_version, scikit_learn, cloud_version, pytz, psycopg2_binary
