from dataclasses import dataclass, field
from typing import Optional, Dict

@dataclass
class CustomDataConfig:
    data_files: Optional[Dict[str, str]] = field(
        default=None,
        metadata={"help": "Custom local dataset files. Supports 'train' and 'validation' keys."}
    )
    CustomDatasetName: Optional[str]=field(
        default=None,
        metadata={"help": 'Custom dataset name '}
    )
    system_promptss: Optional[str]=field(
        default=None,
        metadata={"help": 'system prompt '}
    )
    standard_rules_2: Optional[str]=field(
        default=None,
        metadata={"help": 'standard rules for underwriting '}
    )
    model_promptss: Optional[str]=field(
        default=None,
        metadata={"help": 'model prompt '}
    )
    model_upload_blob_name: Optional[str]=field(
        default=None,
        metadata={"help": 'model destination blob name for storing trained model '}
    )
    bucket_name: Optional[str]=field(
        default=None,
        metadata={"help": 'bucket name in storage to upload and download files '}
    )
    session_id: Optional[str]=field(
        default=None,
        metadata={"help": 'session id for the training job '}
    )
    
    
    
