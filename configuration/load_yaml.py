import yaml

with open('./configuration/parameters.yaml') as file:
    hyperparameters = yaml.load(file, Loader= yaml.FullLoader)
    
class LoadConfig:
    def __init__(self):
        
        self.trainer: str = hyperparameters['mail_id']
        self.app: str = hyperparameters['app']
        self.project_name: str = hyperparameters['project_name']
        self.model_name: str = hyperparameters['model_name']
        self.data_name: str = hyperparameters['data_name']
        self.golden_dataset : str = hyperparameters['golden_dataset']
        self.classes: int = hyperparameters['classes']
        self.data_segrigation_ratio = hyperparameters['data_segrigation_ratio']
        self.min_images_count = hyperparameters['min_images_count']
        self.img_dimensions = hyperparameters['img_dimensions']
        self.architecture = hyperparameters['architecture']
        self.data_grades = hyperparameters['data_grades']
        self.epochs: int = hyperparameters['epochs']
        self.train_batch_size: int = hyperparameters['train_batch_size']
        self.val_batch_size: int = hyperparameters['val_batch_size']
        self.jit_compile: bool = hyperparameters['jit_compile']
        self.warmup_calculations = hyperparameters['warmup_calculation']
        self.gpus = hyperparameters['gpus']
        self.cloud = hyperparameters['cloud']
        self.image_size=hyperparameters["img_dimensions"]

def load_parms():    
    return LoadConfig()