import re
from urllib.parse import urlsplit, urlencode

import os
import yaml
import requests
import kfp
import kfp.dsl as dsl
import urllib3

from configuration.load_yaml import load_parms

def get_dex_session_cookies(api_url, dex_username, dex_password, dex_auth_type="local", skip_tls_verify=True):
    if skip_tls_verify:
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    if dex_auth_type not in ["ldap", "local"]:
        raise ValueError(f"Invalid `dex_auth_type` '{dex_auth_type}', must be one of: ['ldap', 'local']")

    s = requests.Session()

    resp = s.get(api_url, allow_redirects=True, verify=not skip_tls_verify)
    if resp.status_code == 403:
        url_obj = urlsplit(resp.url)
        url_obj = url_obj._replace(
            path="/oauth2/start", query=urlencode({"rd": url_obj.path})
        )
        resp = s.get(url_obj.geturl(), allow_redirects=True, verify=not skip_tls_verify)
    elif resp.status_code != 200:
        raise RuntimeError(f"HTTP status code '{resp.status_code}' for GET against: {api_url}")

    if len(resp.history) == 0:
        return ""

    url_obj = urlsplit(resp.url)
    if re.search(r"/auth$", url_obj.path):
        url_obj = url_obj._replace(path=re.sub(r"/auth$", f"/auth/{dex_auth_type}", url_obj.path))

    if re.search(r"/auth/.*/login$", url_obj.path):
        dex_login_url = url_obj.geturl()
    else:
        resp = s.get(url_obj.geturl(), allow_redirects=True, verify=not skip_tls_verify)
        if resp.status_code != 200:
            raise RuntimeError(f"HTTP status code '{resp.status_code}' for GET against: {url_obj.geturl()}")
        dex_login_url = resp.url

    resp = s.post(
        dex_login_url,
        data={"login": dex_username, "password": dex_password},
        allow_redirects=True,
        verify=not skip_tls_verify,
    )
    if resp.status_code != 200 or len(resp.history) == 0:
        raise RuntimeError("Dex login failed. Invalid credentials or no redirect.")

    url_obj = urlsplit(resp.url)
    if re.search(r"/approval$", url_obj.path):
        dex_approval_url = url_obj.geturl()
        resp = s.post(
            dex_approval_url,
            data={"approval": "approve"},
            allow_redirects=True,
            verify=not skip_tls_verify,
        )
        if resp.status_code != 200:
            raise RuntimeError(f"Approval step failed at: {dex_approval_url}")

    return "; ".join([f"{c.name}={c.value}" for c in s.cookies])


with open('./configuration/parameters.yaml') as file:
    hyperparameters = yaml.load(file, Loader=yaml.FullLoader)

config_file = load_parms()
USERNAME = str(hyperparameters['mail_id'])
PASSWORD = str(hyperparameters['password'])
NAMESPACE = str(hyperparameters['namespace'])
HOST = 'http://kubeflow-retail.vishwamcorp.com'
TRAINING_IMAGE = "asia-docker.pkg.dev/jiovishwam-retail/asia.gcr.io/llm-text:" + os.environ.get('VERSION')

gpu_config = config_file.gpus

gpu_count = next((n for k, n in zip(['gpu_1', 'gpu_2', 'gpu_4', 'gpu_8'], [1, 2, 4, 8]) if gpu_config.get(k)), 0)
print("GPU Count:", gpu_count)

@dsl.pipeline(
    name="LLM Text Pipeline",
    description="saving the container data to the pvc"
)
def llm_text():
    dsl.ContainerOp(
        name="Training the Data",
        image=TRAINING_IMAGE,
        command=["./run.sh"],
        pvolumes={"/data": dsl.PipelineVolume(pvc=NAMESPACE + "-nfs")},
    ).set_gpu_limit(gpu_count).add_node_selector_constraint('environment', 'training').add_node_selector_constraint('sku', 'gpu')

try:
    session_cookies = get_dex_session_cookies(
        api_url=HOST,
        dex_username=USERNAME,
        dex_password=PASSWORD,
        dex_auth_type="local",
        skip_tls_verify=True
    )
    client = kfp.Client(host=HOST + '/pipeline', cookies=session_cookies)

    run_result = client.create_run_from_pipeline_func(
        pipeline_func=llm_text,
        namespace=NAMESPACE,
        experiment_name="LLM Text Experiment",
        run_name=f"{config_file.project_name}_{config_file.model_name}_{config_file.architecture}_{config_file.epochs}_{config_file.train_batch_size}",
        arguments={},
    )
    print("Run submitted:", run_result)

except Exception as e:
    print("Error during pipeline submission:", str(e))