variables:
  DOCKER_DRIVER: overlay
stages:
  - build
  - deploy-pipeline

build:
  stage: build
  image:
    name: google/cloud-sdk:latest
  script:
    - curl -sSL https://get.docker.com | sh
    - echo ${RETAIL_CI} | base64 -d > $HOME/gcloud-service-key.json
    - gcloud auth activate-service-account --key-file=${HOME}/gcloud-service-key.json
    - gcloud config set project ${PROJECT}
    - gcloud -q auth configure-docker asia-docker.pkg.dev
    - VERSION="${CI_PIPELINE_IID}.$(echo ${CI_COMMIT_SHA:=latest}| cut -c1-6)"
    - docker build -t asia-docker.pkg.dev/$PROJECT/asia.gcr.io/llm-text:${VERSION} -f Dockerfile .
    - echo "asia-docker.pkg.dev/$PROJECT/asia.gcr.io/llm-text:${VERSION}"
    - docker push asia-docker.pkg.dev/$PROJECT/asia.gcr.io/llm-text:${VERSION}
    - echo
    - echo "Docker image pushed successfully to >> asia-docker.pg.dev/$PROJECT/asia.gcr.io/llm-text:${VERSION}"
    - echo
  tags:
    - retail

deploy-pipeline:
  image: ubuntu:22.04
  stage: deploy-pipeline
  script:
    - export VERSION="${CI_PIPELINE_IID}.$(echo ${CI_COMMIT_SHA:=latest}| cut -c1-6)"
    - echo ${RETAIL_CD} | base64 -d > $HOME/gcloud-service-key.json
    - apt update
    - apt install apt-transport-https ca-certificates gnupg curl python3 python3-pip -y
    - curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | gpg --dearmor -o /usr/share/keyrings/cloud.google.gpg
    - echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
    - apt-get update -y &&  apt-get install google-cloud-cli -y
    - gcloud auth activate-service-account --key-file=$HOME/gcloud-service-key.json
    - gcloud container clusters get-credentials jiovishwam-retail-training-cluster-2 --zone asia-southeast1 --project $PROJECT
    - pip3 install -U pip
    - pip3 install kubeflow-fairing kubeflow-metadata pandas
    - pip3 install 'kfp<2.0.0' 'protobuf<4,>=3.20.0'
    - curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
    - install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
    - python3 pipeline.py
  tags:
    - retail
  