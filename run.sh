#!/bin/bash

uv venv openr1 --python 3.10 && source openr1/bin/activate && uv pip install --upgrade pip

uv pip install vllm==0.8.5.post1

uv pip install setuptools 

uv pip install flash-attn==2.7.3 --no-build-isolation

uv pip install peft

uv pip install google-cloud-storage

GIT_LFS_SKIP_SMUDGE=1 uv pip install -e ".[dev]"

export HF_TOKEN="*************************************"

CUDA_VISIBLE_DEVICES=0,1,2,3 accelerate launch --num_processes 4 --main_process_port 29502 --config_file recipes/accelerate_configs/zero3.yaml src/open_r1/rentalagreementdoc_sft.py --config recipes/Qwen2.5-1.5B-Instruct/sft/config_demo_finetune_RA.yaml

# CUDA_VISIBLE_DEVICES=0,1,2,3 accelerate launch --num_processes 4 --main_process_port 29502 --config_file recipes/accelerate_configs/zero3.yaml src/open_r1/propertydoc_sft.py --config recipes/Qwen2.5-1.5B-Instruct/sft/config_demo_finetune_PD.yaml

# CUDA_VISIBLE_DEVICES=0,1,2,3 accelerate launch --num_processes 4 --main_process_port 29502 --config_file recipes/accelerate_configs/zero3.yaml src/open_r1/retail_sft.py --config recipes/Qwen2.5-1.5B-Instruct/sft/config_demo_finetune_retail.yaml