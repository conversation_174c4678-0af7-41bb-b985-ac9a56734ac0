 
Always return valid JSON only (no explanations).
all the details are related to property which is being handled only
For free-text fields (Address, Pincode, State, Expected_Completion_Time, Land_Area, Builtup_Area, Ownership_Title_Name, Age_of_Property_Years, Other_Description):
Fill with extracted value if present.
If missing → set as "MISSING_FIELD".
For categorical fields (Purchased_From, Construction_Stage, Residential_Property, Commercial_Property, Uses_of_Property, Under_Construction):
Mark "Yes" for all matching options.
Mark "No" for non-matching options.
If not mentioned → "None_of_Above": "Yes", others "No".
Ensure at least one option in every categorical group is "Yes" (or "None_of_Above": "Yes" if nothing applies)
Fields explanation:
    Address: Full property address.
    Pincode: Postal code of the property location.
    State: State where the property is located.
    Purchased_From: How the property was purchased (multiple selections possible).
    Construction_Stage: Current construction status (multiple selections possible).
    Expected_Completion_Time: Estimated time for construction completion.
    Land_Area: Total land area of the property.
    Builtup_Area: Total built-up area of the property.
    Ownership_Title_Name: Name on the ownership title.
    Residential_Property: Type of residential property (pickone from all options).
    Commercial_Property: Type of commercial property  (multiple selections possible).
    Uses_of_Property: Intended use of the property (multiple selections possible).
    Age_of_Property_Years: Age of the property in years.
    Under_Construction: Whether the property is currently under construction.
    Note: 
**if ouput is in any other language other than English, translate it to English and then give the output in JSON format.**
output schema/json format:

{
    "Address": "",
    "Pincode": "",
    "State": "",
    "Purchased_From": {
        "Builder": "Yes/No",
        "Society": "Yes/No",
        "Development Authority": "Yes/No",
        "Resale": "Yes/No",
        "Self_Construction": "Yes/No",
        "None_of_Above": "Yes/No"
    },
    "Construction_Stage": {
        "To_Commence": "Yes/No",
        "Ready": "Yes/No",
        "Other": "Yes/No",
        "Other_Description": "",
        "None_of_Above": "Yes/No"
    },
    "Expected_Completion_Time": "",
    "Land_Area": "",
    "Builtup_Area": "",
    "Ownership_Title_Name": "",
    "Residential_Property": {
        "Bungalow": "Yes/No",
        "Row_House": "Yes/No",
        "Plot": "Yes/No",
        "Mixed_Usage": "Yes/No",
        "Flat": "Yes/No",
        "None_of_Above": "Yes/No"
    },
    "Commercial_Property": {
        "Shop": "Yes/No",
        "Showroom": "Yes/No",
        "Office": "Yes/No",
        "Plot_Commercial": "Yes/No",
        "None_of_Above": "Yes/No"
    },
    "Uses_of_Property": {
        "Self": "Yes/No",
        "Family": "Yes/No",
        "Investment": "Yes/No",
        "Rent": "Yes/No",
        "None_of_Above": "Yes/No"
    },
    "Age_of_Property_Years": "",
    "Under_Construction": {
        "Yes": "Yes/No",
        "No": "Yes/No",
        "None_of_Above": "Yes/No"
    }
}

Return the valid JSON object WITHOUT any explanatory text, markdown formatting blocks (like ```json or ```), or surrounding characters. Start the output directly with the opening curly brace { of the JSON.
