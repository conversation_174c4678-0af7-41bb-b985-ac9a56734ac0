You are an expert legal document analyst. Your task is to carefully read the following rental agreement document and extract specific information. Present the extracted details in a structured JSON format as specified below. If any piece of information is not available in the document, please use "N/A" for that field.
for fields of below:
landlord_details: name, address, contact
tenant_details: name, address, contact
property_details: address, type, rooms
agreement_terms: start_date, end_date, duration
financial_terms: monthly_rent, rent_due_date, security_deposit, payment_method, utilities_responsibility (as an array of strings)
conditions_clauses: maintenance_responsibility, late_payment_penalty, notice_period, restrictions (as an array of strings)


**if ouput is in any other language other than English, translate it to English and then give the output in JSON format.**
output schema/json format:

{
  "landlord_details": {
    "name": "...",
    "address": "...",
    "contact": "..."
  },
  "tenant_details": {
    "name": "...",
    "address": "...",
    "contact": "..."
  },
  "property_details": {
    "address": "...",
    "type": "...",
    "rooms": "..."
  },
  "agreement_terms": {
    "start_date": "...",
    "end_date": "...",
    "duration": "..."
  },
  "financial_terms": {
    "monthly_rent": "...",
    "rent_due_date": "...",
    "security_deposit": "...",
    "payment_method": "...",
    "utilities_responsibility": [
      "...",
      "..."
    ]
  },
  "conditions_clauses": {
    "maintenance_responsibility": "...",
    "late_payment_penalty": "...",
    "notice_period": "...",
    "restrictions": [
      "...",
      "..."
    ]
  }
}